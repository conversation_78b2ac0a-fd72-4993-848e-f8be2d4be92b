import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;

public class demo {
    // 最大重定向次数，防止无限循环
    private static final int MAX_REDIRECTS = 5;

    // 发送GET请求并返回JSON字符串
    public static String sendGetRequest(String urlString) {
        HttpURLConnection connection = null;
        BufferedReader reader = null;
        StringBuilder responseJson = new StringBuilder();

        try {
            URL url = new URL(urlString);
            connection = (HttpURLConnection) url.openConnection();

            // 设置请求方法和头信息
            connection.setRequestMethod("GET");
            connection.setRequestProperty("Authorization", "Bearer 123123");
            connection.setRequestProperty("Tenant-Id", "162");
            connection.setRequestProperty("User-Agent", "Apifox/1.0.0 (https://apifox.com)");
            connection.setRequestProperty("Accept", "*/*");
            connection.setRequestProperty("Host", "api.jcy360.cn");
            connection.setRequestProperty("Connection", "keep-alive");
            connection.setDoOutput(false);

            // 获取响应流
            InputStream inputStream;
            if (connection.getResponseCode() >= 200 && connection.getResponseCode() < 300) {
                inputStream = connection.getInputStream();
            } else {
                inputStream = connection.getErrorStream();
            }

            // 读取响应内容
            if (inputStream != null) {
                reader = new BufferedReader(
                        new InputStreamReader(inputStream, StandardCharsets.UTF_8)
                );
                String line;
                while ((line = reader.readLine()) != null) {
                    responseJson.append(line);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        } finally {
            // 关闭资源
            if (reader != null) {
                try { reader.close(); } catch (IOException e) { e.printStackTrace(); }
            }
            if (connection != null) {
                connection.disconnect();
            }
        }

        return responseJson.toString();
    }

    // 纯字符串解析方式提取data.url（适用于JDK 1.8）
    public static String extractUrlFromJson(String jsonResponse) {
        if (jsonResponse == null || jsonResponse.isEmpty()) {
            System.out.println("JSON响应为空");
            return null;
        }

        try {
            // 清除JSON中的空格，避免格式影响解析
            String json = jsonResponse.replaceAll("\\s+", "");

            // 查找"data":{的位置
            int urlStart = json.indexOf("\"data\":");
            if (urlStart == -1) {
                System.out.println("未找到data字段");
                return null;
            }

            int len = "\"data\":".length();

            // 从data字段开始查找"url":"
            int urlEnd = json.indexOf(",\"msg\"", urlStart);
            if (urlEnd == -1) {
                System.out.println("data字段中未找到url");
                return null;
            }

            // 提取url值
            return json.substring(urlStart + len + 1, urlEnd - 1);

        } catch (Exception e) {
            System.out.println("解析JSON失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    public static void main(String[] args) throws IOException {
        String apiUrl = "http://api.jcy360.cn/admin-api/mall/vop-access-token/authorize/url";

        // 发送请求获取JSON响应
        String jsonResponse = sendGetRequest(apiUrl);
        if (jsonResponse != null) {
            System.out.println("完整JSON响应:");
            System.out.println(jsonResponse);

            // 提取url
            String url = extractUrlFromJson(jsonResponse);

            if (url != null) {
                System.out.println("\n提取到的url: " + url);
            } else {
                System.out.println("\n未能提取到有效的url");
            }

            sendHttpRequestWithRedirects(url, 0);
        } else {
            System.out.println("请求失败，无法获取JSON响应");
        }
    }
    
    /**
     * 发送HTTP请求并处理重定向
     * @param url 请求的URL
     * @param redirectCount 当前重定向次数
     * @throws IOException 如果发生I/O错误
     */
    private static void sendHttpRequestWithRedirects(String url, int redirectCount) throws IOException {
        // 检查是否超过最大重定向次数
        if (redirectCount >= MAX_REDIRECTS) {
            System.out.println("已达到最大重定向次数 (" + MAX_REDIRECTS + ")，终止请求");
            return;
        }
        
        URL requestUrl = new URL(url);
        HttpURLConnection connection = (HttpURLConnection) requestUrl.openConnection();
        
        // 禁用自动重定向，以便手动处理
        connection.setInstanceFollowRedirects(false);
        connection.setRequestMethod("GET");
        connection.setConnectTimeout(5000);
        connection.setReadTimeout(5000);
        
        System.out.println("发送请求到: " + url);
        int responseCode = connection.getResponseCode();
        System.out.println("响应状态码: " + responseCode);
        
        // 检查是否是重定向响应 (3xx状态码)
        if (responseCode == HttpURLConnection.HTTP_MOVED_PERM || 
            responseCode == HttpURLConnection.HTTP_MOVED_TEMP || 
            responseCode == HttpURLConnection.HTTP_SEE_OTHER) {
            
            // 获取重定向的位置
            String redirectUrl = connection.getHeaderField("Location");
            System.out.println("302 重定向到: " + redirectUrl);
            
            // 关闭当前连接
            connection.disconnect();
            
            // 递归处理重定向
            sendHttpRequestWithRedirects(redirectUrl, redirectCount + 1);
        } else {
            // 非重定向响应，读取响应内容
            System.out.println("\n响应内容:");
            try (BufferedReader in = new BufferedReader(
                    new InputStreamReader(connection.getInputStream()))) {
                String inputLine;
                StringBuilder response = new StringBuilder();
                
                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                // 只打印前500个字符，避免输出过多
                if (response.length() > 500) {
                    System.out.println(response.substring(0, 500) + "...");
                } else {
                    System.out.println(response.toString());
                }
            }
            connection.disconnect();
        }
    }
}
