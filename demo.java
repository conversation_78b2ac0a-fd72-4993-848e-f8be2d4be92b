import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class demo {
    // 最大重定向次数，防止无限循环
    private static final int MAX_REDIRECTS = 5;

    // 使用Hutool发送GET请求并返回JSON字符串
    public static String sendGetRequest(String urlString) {
        try {
            // 构建请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("Authorization", "Bearer 123123");
            headers.put("Tenant-Id", "162");
            headers.put("User-Agent", "Apifox/1.0.0 (https://apifox.com)");
            headers.put("Accept", "*/*");
            headers.put("Host", "api.jcy360.cn");
            headers.put("Connection", "keep-alive");

            // 使用Hutool发送GET请求
            HttpResponse response = HttpRequest.get(urlString)
                    .headerMap(headers, true)
                    .timeout(5000)
                    .execute();

            // 检查响应状态
            if (response.isOk()) {
                return response.body();
            } else {
                System.out.println("请求失败，状态码: " + response.getStatus());
                return response.body(); // 返回错误响应内容
            }

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    // 使用Hutool的JSONUtil解析JSON并提取data.url
    public static String extractUrlFromJson(String jsonResponse) {
        if (jsonResponse == null || jsonResponse.isEmpty()) {
            System.out.println("JSON响应为空");
            return null;
        }

        try {
            // 使用Hutool解析JSON
            JSONObject jsonObject = JSONUtil.parseObj(jsonResponse);

            // 检查是否有data字段
            if (!jsonObject.containsKey("data")) {
                System.out.println("未找到data字段");
                return null;
            }

            // 获取data字段的值
            String dataValue = jsonObject.getStr("data");
            if (dataValue == null || dataValue.isEmpty()) {
                System.out.println("data字段为空");
                return null;
            }

            return dataValue;

        } catch (Exception e) {
            System.out.println("解析JSON失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    public static void main(String[] args) throws IOException {
        String apiUrl = "http://api.jcy360.cn/admin-api/mall/vop-access-token/authorize/url";

        // 发送请求获取JSON响应
        String jsonResponse = sendGetRequest(apiUrl);
        if (jsonResponse != null) {
            System.out.println("完整JSON响应:");
            System.out.println(jsonResponse);

            // 提取url
            String url = extractUrlFromJson(jsonResponse);

            if (url != null) {
                System.out.println("\n提取到的url: " + url);
            } else {
                System.out.println("\n未能提取到有效的url");
            }

            sendHttpRequestWithRedirects(url, 0);
        } else {
            System.out.println("请求失败，无法获取JSON响应");
        }
    }
    
    /**
     * 使用Hutool发送HTTP请求并处理重定向
     * @param url 请求的URL
     * @param redirectCount 当前重定向次数
     */
    private static void sendHttpRequestWithRedirects(String url, int redirectCount) {
        // 检查是否超过最大重定向次数
        if (redirectCount >= MAX_REDIRECTS) {
            System.out.println("已达到最大重定向次数 (" + MAX_REDIRECTS + ")，终止请求");
            return;
        }

        try {
            System.out.println("发送请求到: " + url);

            // 使用Hutool发送请求，禁用自动重定向
            HttpResponse response = HttpRequest.get(url)
                    .setFollowRedirects(false)
                    .timeout(5000)
                    .execute();

            int responseCode = response.getStatus();
            System.out.println("响应状态码: " + responseCode);

            // 检查是否是重定向响应 (3xx状态码)
            if (responseCode == 301 || responseCode == 302 || responseCode == 303) {
                // 获取重定向的位置
                String redirectUrl = response.header("Location");
                System.out.println("重定向到: " + redirectUrl);

                // 递归处理重定向
                sendHttpRequestWithRedirects(redirectUrl, redirectCount + 1);
            } else {
                // 非重定向响应，读取响应内容
                System.out.println("\n响应内容:");
                String responseBody = response.body();

                // 只打印前500个字符，避免输出过多
                if (responseBody.length() > 500) {
                    System.out.println(responseBody.substring(0, 500) + "...");
                } else {
                    System.out.println(responseBody);
                }
            }

        } catch (Exception e) {
            System.out.println("请求失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
