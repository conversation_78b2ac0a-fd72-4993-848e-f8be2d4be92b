# Hutool HttpUtil 示例项目

这个项目展示了如何使用 Hutool 的 HttpUtil 来替代原生的 Java HttpURLConnection，实现更简洁和易用的HTTP请求功能。

## 项目结构

```
├── pom.xml                 # Maven配置文件，包含Hutool依赖
├── demo.java              # 重构后的主要代码，使用Hutool HttpUtil
├── HutoolHttpExample.java # Hutool HttpUtil的各种用法示例
└── README.md              # 项目说明文档
```

## 主要改进

### 1. 代码简化
- **原来**: 需要手动管理 HttpURLConnection、InputStream、BufferedReader 等资源
- **现在**: 使用 Hutool 的 HttpRequest 和 HttpResponse，一行代码完成请求

### 2. 请求头设置
```java
// 原来的方式
connection.setRequestProperty("Authorization", "Bearer 123123");
connection.setRequestProperty("Tenant-Id", "162");
// ... 更多请求头

// 现在的方式
Map<String, String> headers = new HashMap<>();
headers.put("Authorization", "Bearer 123123");
headers.put("Tenant-Id", "162");
HttpRequest.get(url).headerMap(headers, true)
```

### 3. JSON解析
```java
// 原来：手动字符串解析
String json = jsonResponse.replaceAll("\\s+", "");
int urlStart = json.indexOf("\"data\":");
// ... 复杂的字符串操作

// 现在：使用Hutool的JSONUtil
JSONObject jsonObject = JSONUtil.parseObj(jsonResponse);
String dataValue = jsonObject.getStr("data");
```

### 4. 异常处理
- **原来**: 需要在 finally 块中手动关闭资源
- **现在**: Hutool 自动管理资源，无需手动关闭

## 依赖配置

项目使用 Maven 管理依赖，在 `pom.xml` 中添加了 Hutool：

```xml
<dependency>
    <groupId>cn.hutool</groupId>
    <artifactId>hutool-all</artifactId>
    <version>5.8.22</version>
</dependency>
```

## 运行项目

### 1. 安装依赖
```bash
mvn clean install
```

### 2. 编译运行主程序
```bash
mvn compile exec:java -Dexec.mainClass="demo"
```

### 3. 运行示例程序
```bash
mvn compile exec:java -Dexec.mainClass="HutoolHttpExample"
```

## 主要功能

### demo.java
重构后的主要功能：
1. **sendGetRequest()**: 使用 Hutool 发送 GET 请求
2. **extractUrlFromJson()**: 使用 Hutool 的 JSONUtil 解析 JSON
3. **sendHttpRequestWithRedirects()**: 处理 HTTP 重定向

### HutoolHttpExample.java
包含多种 Hutool HttpUtil 的使用示例：
1. 简单的 GET 请求
2. 带参数的 GET 请求
3. 带请求头的 GET 请求
4. POST 请求发送 JSON 数据
5. POST 请求发送表单数据
6. 文件下载
7. 高级配置和错误处理

## Hutool HttpUtil 主要优势

1. **简洁的API**: 一行代码完成HTTP请求
2. **自动资源管理**: 无需手动关闭连接和流
3. **丰富的功能**: 支持各种HTTP方法、请求头、参数、文件上传下载等
4. **异常处理**: 统一的异常处理机制
5. **JSON支持**: 内置JSON解析功能
6. **链式调用**: 支持流畅的链式API调用

## 常用方法

### 简单请求
```java
// GET请求
String result = HttpUtil.get("http://example.com");

// POST请求
String result = HttpUtil.post("http://example.com", "data");
```

### 复杂请求
```java
HttpResponse response = HttpRequest.get("http://example.com")
    .header("Authorization", "Bearer token")
    .timeout(5000)
    .execute();
```

### JSON处理
```java
JSONObject json = JSONUtil.parseObj(jsonString);
String value = json.getStr("key");
```

## 注意事项

1. 确保网络连接正常
2. 根据实际需要调整超时时间
3. 处理可能的异常情况
4. 对于敏感信息（如API密钥），建议使用配置文件管理

## 更多资源

- [Hutool官方文档](https://hutool.cn/)
- [Hutool HTTP工具文档](https://hutool.cn/docs/#/http/Http%E5%AE%A2%E6%88%B7%E7%AB%AF%E5%B7%A5%E5%85%B7-HttpUtil)
