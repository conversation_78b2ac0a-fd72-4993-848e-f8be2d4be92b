import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import java.util.HashMap;
import java.util.Map;

/**
 * Hutool HttpUtil 使用示例
 * 展示各种HTTP请求方式和功能
 */
public class HutoolHttpExample {

    public static void main(String[] args) {
        // 示例1: 简单的GET请求
        simpleGetRequest();
        
        // 示例2: 带参数的GET请求
        getRequestWithParams();
        
        // 示例3: 带请求头的GET请求
        getRequestWithHeaders();
        
        // 示例4: POST请求发送JSON数据
        postRequestWithJson();
        
        // 示例5: POST请求发送表单数据
        postRequestWithForm();
        
        // 示例6: 文件下载
        downloadFile();
    }

    /**
     * 示例1: 最简单的GET请求
     */
    public static void simpleGetRequest() {
        System.out.println("=== 示例1: 简单GET请求 ===");
        try {
            // 最简单的方式
            String result = HttpUtil.get("https://httpbin.org/get");
            System.out.println("响应: " + result.substring(0, Math.min(200, result.length())) + "...");
        } catch (Exception e) {
            System.out.println("请求失败: " + e.getMessage());
        }
        System.out.println();
    }

    /**
     * 示例2: 带参数的GET请求
     */
    public static void getRequestWithParams() {
        System.out.println("=== 示例2: 带参数的GET请求 ===");
        try {
            // 方式1: 直接在URL中拼接参数
            String result1 = HttpUtil.get("https://httpbin.org/get?param1=value1&param2=value2");
            
            // 方式2: 使用参数Map
            Map<String, Object> params = new HashMap<>();
            params.put("name", "张三");
            params.put("age", 25);
            params.put("city", "北京");
            
            String result2 = HttpUtil.get("https://httpbin.org/get", params);
            System.out.println("带参数的响应: " + result2.substring(0, Math.min(300, result2.length())) + "...");
        } catch (Exception e) {
            System.out.println("请求失败: " + e.getMessage());
        }
        System.out.println();
    }

    /**
     * 示例3: 带请求头的GET请求
     */
    public static void getRequestWithHeaders() {
        System.out.println("=== 示例3: 带请求头的GET请求 ===");
        try {
            // 构建请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("User-Agent", "MyApp/1.0");
            headers.put("Authorization", "Bearer your-token-here");
            headers.put("Accept", "application/json");

            // 使用HttpRequest构建复杂请求
            HttpResponse response = HttpRequest.get("https://httpbin.org/headers")
                    .headerMap(headers, true)
                    .timeout(10000)
                    .execute();

            System.out.println("状态码: " + response.getStatus());
            System.out.println("响应头: " + response.headers());
            System.out.println("响应体: " + response.body().substring(0, Math.min(300, response.body().length())) + "...");
        } catch (Exception e) {
            System.out.println("请求失败: " + e.getMessage());
        }
        System.out.println();
    }

    /**
     * 示例4: POST请求发送JSON数据
     */
    public static void postRequestWithJson() {
        System.out.println("=== 示例4: POST请求发送JSON ===");
        try {
            // 构建JSON数据
            JSONObject jsonData = new JSONObject();
            jsonData.put("name", "李四");
            jsonData.put("email", "<EMAIL>");
            jsonData.put("age", 30);

            // 发送POST请求
            HttpResponse response = HttpRequest.post("https://httpbin.org/post")
                    .header("Content-Type", "application/json")
                    .body(jsonData.toString())
                    .timeout(10000)
                    .execute();

            System.out.println("状态码: " + response.getStatus());
            System.out.println("响应: " + response.body().substring(0, Math.min(400, response.body().length())) + "...");
        } catch (Exception e) {
            System.out.println("请求失败: " + e.getMessage());
        }
        System.out.println();
    }

    /**
     * 示例5: POST请求发送表单数据
     */
    public static void postRequestWithForm() {
        System.out.println("=== 示例5: POST请求发送表单数据 ===");
        try {
            // 构建表单数据
            Map<String, Object> formData = new HashMap<>();
            formData.put("username", "testuser");
            formData.put("password", "testpass");
            formData.put("remember", true);

            // 发送POST表单请求
            String result = HttpUtil.post("https://httpbin.org/post", formData);
            System.out.println("表单提交响应: " + result.substring(0, Math.min(400, result.length())) + "...");
        } catch (Exception e) {
            System.out.println("请求失败: " + e.getMessage());
        }
        System.out.println();
    }

    /**
     * 示例6: 文件下载
     */
    public static void downloadFile() {
        System.out.println("=== 示例6: 文件下载 ===");
        try {
            // 下载文件到指定位置
            long size = HttpUtil.downloadFile("https://httpbin.org/json", "downloaded_file.json");
            System.out.println("文件下载完成，大小: " + size + " 字节");
        } catch (Exception e) {
            System.out.println("下载失败: " + e.getMessage());
        }
        System.out.println();
    }

    /**
     * 高级用法示例：自定义请求配置
     */
    public static void advancedUsage() {
        System.out.println("=== 高级用法示例 ===");
        try {
            HttpResponse response = HttpRequest.get("https://httpbin.org/delay/2")
                    .timeout(5000)                    // 设置超时时间
                    .setFollowRedirects(true)         // 是否跟随重定向
                    .header("User-Agent", "Custom-Agent")
                    .cookie("session", "abc123")      // 设置Cookie
                    .execute();

            if (response.isOk()) {
                System.out.println("请求成功");
                System.out.println("响应时间: " + response.header("Date"));
            } else {
                System.out.println("请求失败，状态码: " + response.getStatus());
            }
        } catch (Exception e) {
            System.out.println("请求失败: " + e.getMessage());
        }
    }

    /**
     * 错误处理示例
     */
    public static void errorHandlingExample() {
        System.out.println("=== 错误处理示例 ===");
        try {
            HttpResponse response = HttpRequest.get("https://httpbin.org/status/404")
                    .execute();

            if (response.isOk()) {
                System.out.println("请求成功: " + response.body());
            } else {
                System.out.println("请求失败，状态码: " + response.getStatus());
                System.out.println("错误信息: " + response.body());
            }
        } catch (Exception e) {
            System.out.println("网络异常: " + e.getMessage());
        }
    }
}
